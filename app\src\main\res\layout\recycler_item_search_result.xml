<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/item_head_image_padding">

    <ImageView
        android:id="@+id/iv_item_head_img"
        android:layout_width="@dimen/item_image_size"
        android:layout_height="@dimen/item_image_size"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/tv_item_name"
        android:layout_width="@dimen/item_image_size"
        android:layout_height="wrap_content"
        android:background="@color/color_bg_name"
        android:gravity="center"
        android:textColor="@android:color/white" />
</LinearLayout>
