apply plugin: 'com.android.application'

android {
    compileSdk 35
    buildToolsVersion '35.0.0'

    defaultConfig {
        applicationId "cmc.ccam.terminal"
        namespace "cmc.ccam.terminal"
        minSdkVersion 21
        targetSdkVersion 35
        versionCode 1
        versionName "1.0"
        vectorDrawables.useSupportLibrary = true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    dataBinding {
        enabled = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }


}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    // androidx.  preference,database,widget,etc.
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation "androidx.room:room-runtime:2.6.1"
    annotationProcessor "androidx.room:room-compiler:2.6.1"
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.preference:preference:1.2.1'

    // material design
    implementation 'com.google.android.material:material:1.12.0'
    // image load lib
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    // rxjava
    implementation 'io.reactivex.rxjava2:rxjava:2.2.21'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    // crash dump
    implementation 'com.iqiyi.xcrash:xcrash-android-lib:3.1.0'

    // for test
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

}
