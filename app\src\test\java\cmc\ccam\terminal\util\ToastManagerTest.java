package cmc.ccam.terminal.util;

import android.content.Context;
import android.widget.Toast;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import static org.junit.Assert.*;

/**
 * Unit tests for ToastManager to verify toast throttling and duplicate prevention
 */
@RunWith(RobolectricTestRunner.class)
public class ToastManagerTest {

    @Mock
    private Context mockContext;
    
    private ToastManager toastManager;
    private Context context;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        toastManager = ToastManager.getInstance();
        context = RuntimeEnvironment.application;
        // Clear any previous state
        toastManager.clearDuplicateCache();
    }

    @Test
    public void testSingletonInstance() {
        ToastManager instance1 = ToastManager.getInstance();
        ToastManager instance2 = ToastManager.getInstance();
        assertSame("ToastManager should be singleton", instance1, instance2);
    }

    @Test
    public void testBasicToastFunctionality() {
        // Test that basic toast functionality works
        toastManager.showToast(context, "Test message");
        assertEquals("Toast count should be 1", 1, toastManager.getCurrentToastCount());
    }

    @Test
    public void testDuplicateMessagePrevention() {
        String message = "Duplicate test message";
        
        // Show the same message multiple times quickly
        toastManager.showToast(context, message);
        toastManager.showToast(context, message);
        toastManager.showToast(context, message);
        
        // Should only count as one toast due to duplicate prevention
        assertEquals("Duplicate messages should be prevented", 1, toastManager.getCurrentToastCount());
    }

    @Test
    public void testRateLimiting() {
        // Try to show more toasts than the limit allows
        for (int i = 0; i < 35; i++) {
            toastManager.showToast(context, "Message " + i);
        }
        
        // Should be limited to 30 toasts per minute
        assertTrue("Toast count should be limited", toastManager.getCurrentToastCount() <= 30);
        assertTrue("Rate limiting should be active", toastManager.isRateLimited());
    }

    @Test
    public void testForceShowToast() {
        // Fill up the rate limit
        for (int i = 0; i < 35; i++) {
            toastManager.showToast(context, "Message " + i);
        }
        
        // Force show should bypass rate limiting
        toastManager.forceShowToast(context, "Force message", Toast.LENGTH_SHORT);
        
        // This test mainly verifies no exceptions are thrown
        assertTrue("Force show should work even when rate limited", true);
    }

    @Test
    public void testNullAndEmptyMessages() {
        int initialCount = toastManager.getCurrentToastCount();
        
        // Test null message
        toastManager.showToast(context, null);
        assertEquals("Null message should not increase count", initialCount, toastManager.getCurrentToastCount());
        
        // Test empty message
        toastManager.showToast(context, "");
        assertEquals("Empty message should not increase count", initialCount, toastManager.getCurrentToastCount());
        
        // Test whitespace-only message
        toastManager.showToast(context, "   ");
        assertEquals("Whitespace-only message should not increase count", initialCount, toastManager.getCurrentToastCount());
    }

    @Test
    public void testClearDuplicateCache() {
        String message = "Cache test message";
        
        // Show a message
        toastManager.showToast(context, message);
        assertEquals("First message should be shown", 1, toastManager.getCurrentToastCount());
        
        // Clear cache
        toastManager.clearDuplicateCache();
        
        // Show the same message again - should be allowed after cache clear
        toastManager.showToast(context, message);
        assertEquals("Message should be shown after cache clear", 2, toastManager.getCurrentToastCount());
    }

    @Test
    public void testLongToastMethod() {
        toastManager.showLongToast(context, "Long toast test");
        assertEquals("Long toast should be counted", 1, toastManager.getCurrentToastCount());
    }
}
