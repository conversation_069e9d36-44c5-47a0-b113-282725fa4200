@echo off
echo ========================================
echo   CCam Terminal - Settings Fix Test
echo ========================================
echo.

set PACKAGE_NAME=cmc.ccam.terminal

echo [1/6] Cleaning project...
call gradlew.bat clean
if %ERRORLEVEL% neq 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)

echo.
echo [2/6] Building debug APK...
call gradlew.bat assembleDebug
if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo [3/6] Installing APK...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %ERRORLEVEL% neq 0 (
    echo ERROR: Install failed
    pause
    exit /b 1
)

echo.
echo [4/6] Starting test activity...
adb shell am start -n %PACKAGE_NAME%/.ui.activity.TestSettingsActivity

echo.
echo [5/6] Monitoring logs for 10 seconds...
timeout /t 2 /nobreak > nul
adb logcat -c
adb shell am start -n %PACKAGE_NAME%/.ui.activity.TestSettingsActivity
timeout /t 3 /nobreak > nul

echo.
echo [6/6] Checking logs...
echo === CLASS LOADING LOGS ===
adb logcat -d | findstr "TestSettings"
echo.
echo === ERROR LOGS ===
adb logcat -d | findstr -i "error\|exception\|crash"

echo.
echo ========================================
echo   Test completed!
echo ========================================
echo.
echo If you see "Successfully loaded" messages, the fix worked!
echo If you see ClassNotFoundException, there's still an issue.
echo.
pause
