<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:app="http://schemas.android.com/apk/res-auto">

    <PreferenceCategory app:title="@string/settings_recognize_orient">

        <!-- Test with standard ListPreference first -->
        <ListPreference
            app:defaultValue="@string/default_recognize_orient_priority"
            app:entries="@array/recognize_orient_priority_desc"
            app:entryValues="@array/recognize_orient_priority_values"
            app:key="@string/preference_choose_detect_degree"
            app:title="@string/title_choose_detect_degree"
            app:useSimpleSummaryProvider="true" />

    </PreferenceCategory>

    <PreferenceCategory app:title="@string/settings_camera_preview">

        <SwitchPreferenceCompat
            app:defaultValue="true"
            app:key="@string/preference_enable_face_detect"
            app:title="@string/title_enable_face_detect" />

        <SwitchPreferenceCompat
            app:defaultValue="false"
            app:key="@string/preference_enable_liveness_detect"
            app:title="@string/title_enable_liveness_detect" />

    </PreferenceCategory>

    <PreferenceCategory app:title="@string/settings_threshold">

        <!-- Test with standard EditTextPreference first -->
        <EditTextPreference
            app:key="@string/preference_recognize_threshold"
            app:title="@string/title_recognize_threshold"
            app:useSimpleSummaryProvider="true" />

    </PreferenceCategory>

</PreferenceScreen>
