package cmc.ccam.terminal.common;

import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.util.Log;

import com.arcsoft.face.ErrorInfo;
import com.arcsoft.face.FaceEngine;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;

/**
 * 一些常量设置
 * <p>
 * 双目偏移可在 [参数设置] -> [识别界面适配] 界面中进行设置
 */
public class Constants {

    /**
     * 方式一： 填好APP_ID等参数，进入激活界面激活
     */
    public static final String APP_ID = "GUhYjMeFLiFARWet58gTGotUpr189H6Ch7QFSSMgMUCZ";
    public static final String SDK_KEY = "4LxEGC47QhYUPdXZxemhKQSEiFh6p9rYUSitfEcAAFFF";
    public static final String ACTIVE_KEY = "";

    /**
     * 方式二： 在激活界面读取本地配置文件进行激活
     *
     * 配置文件名称，格式如下：
     * APP_ID:XXXXXXXXXXXXX
     * SDK_KEY:XXXXXXXXXXXXXXX
     * ACTIVE_KEY:XXXX-XXXX-XXXX-XXXX
     */
    public static final String ACTIVE_CONFIG_FILE_NAME = "activeConfig.txt";


    /**
     * 注册图所在路径
     */
    public static final String DEFAULT_REGISTER_FACES_DIR = "sdcard/arcfacedemo/register";


    /**
     * Kiểm tra quyền truy cập file
     */
    private static boolean checkFilePermissions(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                Log.w("file_permission", "File does not exist: " + filePath);
                return false;
            }

            if (!file.canRead()) {
                Log.e("file_permission", "Cannot read file: " + filePath);
                return false;
            }

            Log.d("file_permission", "File accessible: " + filePath + " (size: " + file.length() + " bytes)");
            return true;
        } catch (SecurityException e) {
            Log.e("file_permission", "Security exception accessing file: " + filePath + " - " + e.getMessage());
            return false;
        }
    }

    /**
     * Tìm file license trong các vị trí có thể
     */
    private static String findLicenseFile() {
        // Danh sách các vị trí có thể chứa file license
        String[] possiblePaths = {
            Environment.getExternalStorageDirectory() + "/ArcFacePro32.dat",
            Environment.getExternalStorageDirectory() + "/Download/ArcFacePro32.dat",
            "/sdcard/ArcFacePro32.dat",
            "/sdcard/Download/ArcFacePro32.dat"
        };

        for (String path : possiblePaths) {
            if (checkFilePermissions(path)) {
                File file = new File(path);
                if (file.length() > 100) {
                    Log.d("license_search", "Found valid license file at: " + path);
                    return path;
                }
            }
        }

        Log.e("license_search", "No valid license file found in any location");
        return null;
    }

    /**
     * Log thông tin hệ thống để debug
     */
    private static void logSystemInfo(Context context) {
        Log.d("system_info", "=== SYSTEM INFO ===");
        Log.d("system_info", "Android Version: " + Build.VERSION.RELEASE + " (API " + Build.VERSION.SDK_INT + ")");
        Log.d("system_info", "App Files Dir: " + context.getFilesDir().getAbsolutePath());
        Log.d("system_info", "External Storage Dir: " + Environment.getExternalStorageDirectory().getAbsolutePath());
        Log.d("system_info", "External Storage State: " + Environment.getExternalStorageState());
        Log.d("system_info", "===================");
    }

    public static boolean activeEngineOffline(Context context){
        // Log thông tin hệ thống và quyền để debug
        logSystemInfo(context);
        cmc.ccam.terminal.util.PermissionHelper.logPermissionStatus(context);

        // Kiểm tra quyền truy cập storage
        if (!cmc.ccam.terminal.util.PermissionHelper.hasStoragePermission(context)) {
            Log.e("active_result", "❌ Storage permission not granted");
            Log.i("active_result", "💡 Please grant storage permission in app settings");
            return false;
        }

        boolean result = false;
        String destPath = context.getApplicationContext().getFilesDir() + "/ArcFacePro32.dat";

        File destFile = new File(destPath);
        // Kiểm tra file đích đã tồn tại và có kích thước hợp lệ
        if (destFile.exists() && destFile.length() > 100) {
            Log.d("active_result", "License file already exists in app directory: " + destFile.length() + " bytes");
            result = true;
        } else {
            Log.d("active_result", "Searching for license file...");

            // Tìm file license trong các vị trí có thể
            String sourcePath = findLicenseFile();

            if (sourcePath != null) {
                File sourceFile = new File(sourcePath);
                Log.d("active_result", "Found license file at: " + sourcePath + " (size: " + sourceFile.length() + " bytes)");

                // Thực hiện copy file
                boolean copySuccess = copyFile(sourcePath, destPath);

                if (copySuccess) {
                    // Kiểm tra lại file sau khi copy
                    File copiedFile = new File(destPath);
                    if (copiedFile.exists() && copiedFile.length() > 100) {
                        Log.d("active_result", "✅ Copy successful! Destination file size: " + copiedFile.length() + " bytes");
                        result = true;

                        // Kiểm tra thêm để đảm bảo file integrity
                        if (copiedFile.length() == sourceFile.length()) {
                            Log.d("active_result", "✅ File integrity verified - sizes match");
                        } else {
                            Log.w("active_result", "⚠️ File sizes don't match exactly, but file is valid");
                        }
                    } else {
                        Log.e("active_result", "❌ Copy failed - destination file is empty or too small: " +
                              (copiedFile.exists() ? copiedFile.length() : "file not found") + " bytes");
                    }
                } else {
                    Log.e("active_result", "❌ Copy operation failed");
                }
            } else {
                Log.e("active_result", "❌ No valid license file found in any location");
                Log.i("active_result", "💡 Please ensure ArcFacePro32.dat file is placed in:");
                Log.i("active_result", "   - /sdcard/ArcFacePro32.dat");
                Log.i("active_result", "   - /sdcard/Download/ArcFacePro32.dat");
                Log.i("active_result", "💡 And ensure storage permission is granted");
            }
        }

        Log.d("active_result", "Final activation result: " + (result ? "✅ SUCCESS" : "❌ FAILED"));
        return result;
    }

    public static boolean copyFile(String filePath, String destPath) {
        File originFile = new File(filePath);

        if (!originFile.exists()) {
            Log.e("yw_lisence", "License file not exist at: " + filePath);
            return false;
        }

        if (!originFile.canRead()) {
            Log.e("yw_lisence", "Cannot read license file: " + filePath);
            return false;
        }

        Log.d("yw_lisence", "Source file size: " + originFile.length() + " bytes");

        File destFile = new File(destPath);

        // Tạo thư mục đích nếu chưa tồn tại
        File parentDir = destFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean dirCreated = parentDir.mkdirs();
            Log.d("yw_lisence", "Created parent directory: " + dirCreated);
        }

        BufferedInputStream reader = null;
        BufferedOutputStream writer = null;
        FileOutputStream fos = null;

        try {
            if (!destFile.exists()) {
                destFile.createNewFile();
            }

            reader = new BufferedInputStream(new FileInputStream(originFile));
            fos = new FileOutputStream(destFile);
            writer = new BufferedOutputStream(fos);

            byte[] buffer = new byte[8192]; // Tăng buffer size
            int length;
            long totalBytes = 0;

            while ((length = reader.read(buffer)) != -1) {
                writer.write(buffer, 0, length);
                totalBytes += length;
            }

            // ✅ QUAN TRỌNG: Flush và sync để đảm bảo dữ liệu được ghi xuống disk
            writer.flush();
            fos.flush();
            fos.getFD().sync(); // Force sync to disk

            Log.d("yw_lisence", "Total bytes copied: " + totalBytes);
            Log.d("yw_lisence", "Destination file size after copy: " + destFile.length() + " bytes");

            // Kiểm tra kết quả copy
            if (destFile.length() != originFile.length()) {
                Log.e("yw_lisence", "Copy failed - size mismatch. Expected: " + originFile.length() + ", Got: " + destFile.length());
                return false;
            }

            Log.d("yw_lisence", "File copied successfully");
            return true;

        } catch (SecurityException e) {
            Log.e("yw_lisence", "Permission denied: " + e.getMessage());
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            Log.e("yw_lisence", "IO error during copy: " + e.getMessage());
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            Log.e("yw_lisence", "Unexpected error during copy: " + e.getMessage());
            e.printStackTrace();
            return false;
        } finally {
            // Đóng streams theo thứ tự ngược lại
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    Log.e("yw_lisence", "Error closing writer: " + e.getMessage());
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    Log.e("yw_lisence", "Error closing file output stream: " + e.getMessage());
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    Log.e("yw_lisence", "Error closing reader: " + e.getMessage());
                }
            }
        }
    }

    // 获取sn号
    public static String getSerialNumber() {
        String serial = null;
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method get = c.getMethod("get", String.class);
            serial = (String) get.invoke(c, "ro.serialno");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return serial;
    }

    /**
     * 获取SN号
     *
     * @return
     */
    public static String getSNCode() {
        if (Build.VERSION.SDK_INT >= 23 && Build.VERSION.SDK_INT <= 28) {
            return Build.SERIAL;
        } else {
            return getSerialNumber();
        }
    }

}
