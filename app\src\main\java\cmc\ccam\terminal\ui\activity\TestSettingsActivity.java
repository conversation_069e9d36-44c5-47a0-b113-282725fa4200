package cmc.ccam.terminal.ui.activity;

import android.os.Bundle;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;
import androidx.preference.PreferenceFragmentCompat;

import cmc.ccam.terminal.R;

/**
 * Test activity to debug preference loading issues
 */
public class TestSettingsActivity extends AppCompatActivity {
    private static final String TAG = "TestSettingsActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recognize_settings);

        Log.d(TAG, "Starting TestSettingsActivity");

        // Test if we can load the preference classes
        try {
            Class<?> clazz = Class.forName("cmc.ccam.terminal.preference.ChooseDetectDegreeListPreference");
            Log.d(TAG, "✅ ChooseDetectDegreeListPreference class found: " + clazz.getName());
        } catch (ClassNotFoundException e) {
            Log.e(TAG, "❌ ChooseDetectDegreeListPreference class not found", e);
        }

        try {
            Class<?> clazz = Class.forName("cmc.ccam.terminal.preference.ThresholdPreference");
            Log.d(TAG, "✅ ThresholdPreference class found: " + clazz.getName());
        } catch (ClassNotFoundException e) {
            Log.e(TAG, "❌ ThresholdPreference class not found", e);
        }

        // Try to load the fragment
        if (savedInstanceState == null) {
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.settings, new TestSettingsFragment())
                    .commit();
        }
    }

    public static class TestSettingsFragment extends PreferenceFragmentCompat {
        private static final String TAG = "TestSettingsFragment";

        @Override
        public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
            Log.d(TAG, "onCreatePreferences called");

            try {
                // Try to load the simple version first
                setPreferencesFromResource(R.xml.preferences_recognize_simple, rootKey);
                Log.d(TAG, "✅ Successfully loaded preferences_recognize_simple.xml");
            } catch (Exception e) {
                Log.e(TAG, "❌ Failed to load preferences_recognize_simple.xml", e);
                e.printStackTrace();
                return;
            }

            // If simple version works, try the full version
            try {
                setPreferencesFromResource(R.xml.preferences_recognize, rootKey);
                Log.d(TAG, "✅ Successfully loaded preferences_recognize.xml");
            } catch (Exception e) {
                Log.e(TAG, "❌ Failed to load preferences_recognize.xml", e);
                e.printStackTrace();
            }
        }
    }
}
