# Toast Notification Fix Documentation

## Problem Description

The Android application was hitting Android's 50-toast limit per package, causing the error:
```
Package has already posted 50 toasts. Not showing more. Package=cmc.ccam.terminal
```

## Root Cause Analysis

The main issue was identified in `RecognizeViewModel.onPreviewFrame()` method at line 602, where a toast notification was being shown every time a face with a mask was detected during registration. Since this method is called for every camera preview frame (typically 30+ times per second), it could quickly hit Android's 50-toast limit.

### Problematic Code:
```java
// In RecognizeViewModel.onPreviewFrame()
if (facePreviewInfo.getMask() != MaskInfo.WORN) {
    registerFace(nv21, facePreviewInfoList.get(0));
} else {
    Toast.makeText(ArcFaceApplication.getApplication(), "The registration certificate requires no mask", Toast.LENGTH_SHORT).show();
    updateRegisterStatus(REGISTER_STATUS_DONE);
}
```

## Solution Implementation

### 1. Fixed Camera Preview Toast Issue

**File:** `app/src/main/java/cmc/ccam/terminal/ui/viewmodel/RecognizeViewModel.java`

- Added a flag `maskNotificationShown` to prevent repeated mask notifications
- Replaced direct toast with callback mechanism for better user experience
- Reset the flag when starting a new registration attempt

**Changes:**
```java
// Added flag to prevent repeated notifications
private boolean maskNotificationShown = false;

// In onPreviewFrame() method:
if (!maskNotificationShown) {
    if (onRegisterFinishedCallback != null) {
        onRegisterFinishedCallback.onRegisterFinished(facePreviewInfo, false);
    }
    maskNotificationShown = true;
}

// In prepareRegister() method:
maskNotificationShown = false; // Reset flag for new registration attempt
```

### 2. Created ToastManager Utility Class

**File:** `app/src/main/java/cmc/ccam/terminal/util/ToastManager.java`

A comprehensive toast management system with the following features:

#### Features:
- **Rate Limiting:** Maximum 30 toasts per minute (well below Android's 50-toast limit)
- **Duplicate Prevention:** Prevents identical messages from showing within 3 seconds
- **Thread Safety:** Handles toast display from any thread
- **Singleton Pattern:** Ensures consistent behavior across the application

#### Key Methods:
- `showToast(Context, String)` - Show short toast with throttling
- `showLongToast(Context, String)` - Show long toast with throttling
- `forceShowToast(Context, String, int)` - Bypass throttling for critical messages
- `clearDuplicateCache()` - Clear duplicate message cache
- `isRateLimited()` - Check if rate limiting is active

### 3. Updated BaseActivity

**File:** `app/src/main/java/cmc/ccam/terminal/ui/activity/BaseActivity.java`

Updated the base activity's toast methods to use ToastManager:

```java
protected void showToast(final String s) {
    cmc.ccam.terminal.util.ToastManager.getInstance().showToast(this, s);
}

protected void showLongToast(final String s) {
    cmc.ccam.terminal.util.ToastManager.getInstance().showLongToast(this, s);
}
```

### 4. Updated Direct Toast.makeText() Calls

Updated all direct `Toast.makeText()` calls throughout the application to use ToastManager:

**Files Updated:**
- `app/src/main/java/cmc/ccam/terminal/ui/activity/HomeActivity.java`
- `app/src/main/java/cmc/ccam/terminal/ui/activity/ActivationActivity.java`
- `app/src/main/java/cmc/ccam/terminal/preference/ThresholdLivePreferenceDialogFragmentCompat.java`
- `app/src/main/java/cmc/ccam/terminal/preference/ThresholdPreferenceDialogFragmentCompat.java`
- `app/src/main/java/cmc/ccam/terminal/preference/IntegerPreferenceDialogFragmentCompat.java`

## Testing

### Unit Tests
Created comprehensive unit tests in `app/src/test/java/cmc/ccam/terminal/util/ToastManagerTest.java` to verify:
- Singleton behavior
- Rate limiting functionality
- Duplicate message prevention
- Null/empty message handling
- Cache clearing functionality

### Manual Testing Recommendations
1. **Camera Registration Test:** Try to register a face while wearing a mask multiple times rapidly
2. **Error Handling Test:** Trigger various error conditions to ensure toasts don't accumulate
3. **Long Usage Test:** Use the app continuously for several minutes to verify rate limiting works

## Benefits

1. **Prevents Android Toast Limit:** No more "Package has already posted 50 toasts" errors
2. **Better User Experience:** Eliminates spam of duplicate toast messages
3. **Improved Performance:** Reduces unnecessary toast creation and display
4. **Centralized Management:** All toast behavior is now controlled through one utility class
5. **Configurable Limits:** Easy to adjust rate limits and duplicate prevention timing

## Configuration

The ToastManager can be configured by modifying these constants:

```java
// Maximum toasts per minute
private static final int MAX_TOASTS_PER_MINUTE = 30;

// Time window for counting toasts
private static final long TIME_WINDOW_MS = 60 * 1000;

// Minimum interval between duplicate messages
private static final long MIN_DUPLICATE_INTERVAL_MS = 3000;
```

## Future Considerations

1. **Persistence:** Consider persisting toast counts across app restarts if needed
2. **User Preferences:** Allow users to configure toast behavior in settings
3. **Analytics:** Add logging to monitor toast usage patterns
4. **Alternative UI:** Consider using Snackbars or custom notifications for important messages

## Debug Information

The "set stream to NULL" debug message appears to be a low-level camera/stream debug message that's not directly related to the toast issue and doesn't require immediate attention.
