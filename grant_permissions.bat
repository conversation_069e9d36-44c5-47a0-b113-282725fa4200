@echo off
echo ========================================
echo   CCam Terminal - Permission Grant Tool
echo ========================================
echo.

set PACKAGE_NAME=cmc.ccam.terminal

echo [1/5] Checking device connection...
adb devices
if %ERRORLEVEL% neq 0 (
    echo ERROR: No device connected or ADB not found
    pause
    exit /b 1
)

echo.
echo [2/5] Granting basic storage permissions...
adb shell pm grant %PACKAGE_NAME% android.permission.READ_EXTERNAL_STORAGE
adb shell pm grant %PACKAGE_NAME% android.permission.WRITE_EXTERNAL_STORAGE
adb shell pm grant %PACKAGE_NAME% android.permission.READ_PHONE_STATE

echo.
echo [3/5] Granting MANAGE_EXTERNAL_STORAGE permission (Android 11+)...
adb shell appops set %PACKAGE_NAME% MANAGE_EXTERNAL_STORAGE allow

echo.
echo [4/5] Granting media permissions (Android 13+)...
adb shell pm grant %PACKAGE_NAME% android.permission.READ_MEDIA_IMAGES
adb shell pm grant %PACKAGE_NAME% android.permission.READ_MEDIA_VIDEO
adb shell pm grant %PACKAGE_NAME% android.permission.READ_MEDIA_AUDIO

echo.
echo [5/5] Checking permission status...
echo.
echo === PERMISSION STATUS ===
adb shell dumpsys package %PACKAGE_NAME% | findstr "permission"
echo.
echo === MANAGE_EXTERNAL_STORAGE STATUS ===
adb shell appops get %PACKAGE_NAME% MANAGE_EXTERNAL_STORAGE

echo.
echo ========================================
echo   Permission grant completed!
echo ========================================
echo.
echo Next steps:
echo 1. Copy ArcFacePro32.dat to /sdcard/
echo 2. Open CCam Terminal app
echo 3. Try "Active Offline" function
echo.
pause
