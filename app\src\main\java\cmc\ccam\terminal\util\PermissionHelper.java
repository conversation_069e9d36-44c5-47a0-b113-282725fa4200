package cmc.ccam.terminal.util;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

/**
 * Helper class to handle storage permissions across different Android versions
 */
public class PermissionHelper {
    private static final String TAG = "PermissionHelper";
    
    // Request codes for different permission types
    public static final int REQUEST_STORAGE_PERMISSION = 1001;
    public static final int REQUEST_MANAGE_EXTERNAL_STORAGE = 1002;
    public static final int REQUEST_MEDIA_PERMISSIONS = 1003;

    /**
     * Check if the app has storage permissions based on Android version
     */
    public static boolean hasStoragePermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ (API 30+)
            return Environment.isExternalStorageManager();
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6+ (API 23+)
            return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) 
                   == PackageManager.PERMISSION_GRANTED;
        } else {
            // Below Android 6, permissions are granted at install time
            return true;
        }
    }

    /**
     * Request storage permissions based on Android version
     */
    public static void requestStoragePermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ (API 30+) - Request MANAGE_EXTERNAL_STORAGE
            requestManageExternalStoragePermission(activity);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6-10 (API 23-29) - Request READ_EXTERNAL_STORAGE
            requestLegacyStoragePermission(activity);
        }
        // Below Android 6, no runtime permissions needed
    }

    /**
     * Request MANAGE_EXTERNAL_STORAGE permission for Android 11+
     */
    private static void requestManageExternalStoragePermission(Activity activity) {
        try {
            Log.d(TAG, "Requesting MANAGE_EXTERNAL_STORAGE permission for Android 11+");
            Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
            intent.setData(Uri.parse("package:" + activity.getPackageName()));
            activity.startActivityForResult(intent, REQUEST_MANAGE_EXTERNAL_STORAGE);
        } catch (Exception e) {
            Log.e(TAG, "Failed to request MANAGE_EXTERNAL_STORAGE permission", e);
            // Fallback to general settings
            Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
            activity.startActivityForResult(intent, REQUEST_MANAGE_EXTERNAL_STORAGE);
        }
    }

    /**
     * Request legacy storage permissions for Android 6-10
     */
    private static void requestLegacyStoragePermission(Activity activity) {
        Log.d(TAG, "Requesting legacy storage permissions for Android 6-10");
        String[] permissions = {
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        };
        ActivityCompat.requestPermissions(activity, permissions, REQUEST_STORAGE_PERMISSION);
    }

    /**
     * Request media permissions for Android 13+
     */
    public static void requestMediaPermissions(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Log.d(TAG, "Requesting media permissions for Android 13+");
            String[] permissions = {
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO,
                Manifest.permission.READ_MEDIA_AUDIO
            };
            ActivityCompat.requestPermissions(activity, permissions, REQUEST_MEDIA_PERMISSIONS);
        }
    }

    /**
     * Check if we need to request media permissions (Android 13+)
     */
    public static boolean needsMediaPermissions(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_IMAGES) 
                   != PackageManager.PERMISSION_GRANTED;
        }
        return false;
    }

    /**
     * Get appropriate storage permissions array based on Android version
     */
    public static String[] getStoragePermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+)
            return new String[] {
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO,
                Manifest.permission.READ_MEDIA_AUDIO
            };
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6-12 (API 23-32)
            return new String[] {
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            };
        } else {
            // Below Android 6
            return new String[0];
        }
    }

    /**
     * Show permission explanation dialog
     */
    public static void showPermissionExplanation(Activity activity, String message) {
        new android.app.AlertDialog.Builder(activity)
            .setTitle("Permission Required")
            .setMessage(message)
            .setPositiveButton("Grant Permission", (dialog, which) -> {
                requestStoragePermission(activity);
            })
            .setNegativeButton("Cancel", (dialog, which) -> {
                dialog.dismiss();
            })
            .show();
    }

    /**
     * Check if we should show permission rationale
     */
    public static boolean shouldShowPermissionRationale(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            return !Environment.isExternalStorageManager();
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return ActivityCompat.shouldShowRequestPermissionRationale(activity, 
                Manifest.permission.READ_EXTERNAL_STORAGE);
        }
        return false;
    }

    /**
     * Log current permission status for debugging
     */
    public static void logPermissionStatus(Context context) {
        Log.d(TAG, "=== PERMISSION STATUS ===");
        Log.d(TAG, "Android Version: " + Build.VERSION.RELEASE + " (API " + Build.VERSION.SDK_INT + ")");
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Log.d(TAG, "MANAGE_EXTERNAL_STORAGE: " + Environment.isExternalStorageManager());
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Log.d(TAG, "READ_MEDIA_IMAGES: " + (ContextCompat.checkSelfPermission(context, 
                Manifest.permission.READ_MEDIA_IMAGES) == PackageManager.PERMISSION_GRANTED));
        } else {
            Log.d(TAG, "READ_EXTERNAL_STORAGE: " + (ContextCompat.checkSelfPermission(context, 
                Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED));
        }
        
        Log.d(TAG, "External Storage State: " + Environment.getExternalStorageState());
        Log.d(TAG, "========================");
    }

    /**
     * Open app settings page
     */
    public static void openAppSettings(Activity activity) {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        intent.setData(Uri.parse("package:" + activity.getPackageName()));
        activity.startActivity(intent);
    }
}
