@echo off
echo ========================================
echo Android Build Script with Java Setup
echo ========================================

REM Check if JAVA_HOME is already set
if defined JAVA_HOME (
    echo JAVA_HOME is already set to: %JAVA_HOME%
    goto :build
)

echo JAVA_HOME is not set. Attempting to find Java installation...

REM Common Java installation paths
set JAVA_PATHS[0]="C:\Program Files\Java\jdk-17"
set JAVA_PATHS[1]="C:\Program Files\Java\jdk-11"
set JAVA_PATHS[2]="C:\Program Files\Java\jdk-8"
set JAVA_PATHS[3]="C:\Program Files\Java\jdk1.8.0_*"
set JAVA_PATHS[4]="C:\Program Files (x86)\Java\jdk1.8.0_*"

REM Try to find Java automatically
for /f "tokens=*" %%i in ('dir "C:\Program Files\Java" /b /ad 2^>nul') do (
    if exist "C:\Program Files\Java\%%i\bin\java.exe" (
        set JAVA_HOME=C:\Program Files\Java\%%i
        echo Found Java at: !JAVA_HOME!
        goto :build
    )
)

REM If not found, provide instructions
echo.
echo Java not found automatically. Please:
echo 1. Install Java JDK 17 or higher
echo 2. Set JAVA_HOME environment variable
echo 3. Or manually set JAVA_HOME in this script
echo.
echo Example:
echo set JAVA_HOME=C:\Program Files\Java\jdk-17
echo.
echo Download Java from: https://adoptium.net/
echo.
pause
exit /b 1

:build
echo.
echo Using Java: %JAVA_HOME%
echo.

REM Set PATH to include Java
set PATH=%JAVA_HOME%\bin;%PATH%

REM Verify Java installation
java -version
if errorlevel 1 (
    echo Error: Java not working properly
    pause
    exit /b 1
)

echo.
echo Starting Android build...
echo.

REM Clean and build
call gradlew.bat clean build

if errorlevel 1 (
    echo.
    echo Build failed! Check the error messages above.
    pause
    exit /b 1
) else (
    echo.
    echo Build completed successfully!
    echo.
)

pause
