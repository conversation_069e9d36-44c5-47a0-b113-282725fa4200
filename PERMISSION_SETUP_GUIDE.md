# 🔐 Hướng dẫn cấp quyền Storage cho CCam Terminal

## 🚨 Vấn đề hiện tại

App đang target **Android API 35 (Android 15)** nhưng thiếu quyền truy cập storage cần thiết để đọc file license `ArcFacePro32.dat`.

**Log lỗi hiện tại:**
```
E  Cannot read file: /storage/emulated/0/ArcFacePro32.dat
E  Cannot read file: /sdcard/ArcFacePro32.dat
E  ❌ No valid license file found in any location
```

## ✅ Giải pháp đã triển khai

### 1. **Cập nhật AndroidManifest.xml**

Đã thêm quyền phù hợp cho từng phiên bản Android:

```xml
<!-- Storage permissions for different Android versions -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
    android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
    android:maxSdkVersion="28" />

<!-- For Android 11+ (API 30+) - All files access -->
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" 
    tools:ignore="ScopedStorage" />

<!-- For Android 13+ (API 33+) - Granular media permissions -->
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
```

### 2. **Tạo PermissionHelper.java**

Utility class xử lý quyền theo từng phiên bản Android:
- **Android 6-10**: `READ_EXTERNAL_STORAGE`
- **Android 11+**: `MANAGE_EXTERNAL_STORAGE` 
- **Android 13+**: `READ_MEDIA_*` permissions

### 3. **Cập nhật ActivationActivity**

- Kiểm tra quyền trước khi activation
- Hiển thị dialog giải thích tại sao cần quyền
- Tự động mở Settings nếu cần cấp quyền manual

## 📱 Hướng dẫn cấp quyền cho User

### **Android 11+ (API 30+)**

1. **Mở app CCam Terminal**
2. **Nhấn "Active Offline"**
3. **Nhấn "Grant Permission"** trong dialog
4. **Trong Settings:**
   - Tìm **"CCam Terminal"**
   - Nhấn **"Permissions"**
   - Nhấn **"Files and media"** hoặc **"Storage"**
   - Chọn **"Allow management of all files"**

### **Android 6-10 (API 23-29)**

1. **Mở app CCam Terminal**
2. **Nhấn "Active Offline"**
3. **Nhấn "Allow"** khi được hỏi quyền Storage

### **Android 13+ (API 33+)**

1. **Mở app CCam Terminal**
2. **Nhấn "Active Offline"**
3. **Cấp quyền:**
   - **Photos and videos** - Allow
   - **Music and audio** - Allow

## 🔧 Cách kiểm tra quyền đã được cấp

### **Phương pháp 1: Qua Settings**
1. **Settings > Apps > CCam Terminal > Permissions**
2. **Kiểm tra:**
   - ✅ **Storage** hoặc **Files and media**: Allowed
   - ✅ **Photos and videos**: Allowed (Android 13+)

### **Phương pháp 2: Qua Log**
```bash
adb logcat | grep -E "(PERMISSION STATUS|active_result)"
```

**Log thành công:**
```
D  MANAGE_EXTERNAL_STORAGE: true
D  READ_EXTERNAL_STORAGE: true
D  ✅ Copy successful! Destination file size: XXXX bytes
```

## 🚨 Troubleshooting

### **Lỗi: "Storage permission not granted"**

**Nguyên nhân:**
- App chưa được cấp quyền `MANAGE_EXTERNAL_STORAGE` (Android 11+)
- App chưa được cấp quyền `READ_EXTERNAL_STORAGE` (Android 6-10)

**Giải pháp:**
1. **Vào Settings > Apps > CCam Terminal > Permissions**
2. **Bật tất cả quyền Storage/Files**
3. **Restart app và thử lại**

### **Lỗi: "Cannot read file"**

**Nguyên nhân:**
- File `ArcFacePro32.dat` không tồn tại
- File có kích thước 0 bytes
- Quyền vẫn chưa được cấp đúng cách

**Giải pháp:**
1. **Kiểm tra file tồn tại:**
   ```bash
   adb shell ls -la /sdcard/ArcFacePro32.dat
   ```
2. **Copy file vào đúng vị trí:**
   ```bash
   adb push ArcFacePro32.dat /sdcard/
   ```
3. **Kiểm tra quyền app:**
   ```bash
   adb shell dumpsys package cmc.ccam.terminal | grep permission
   ```

### **Android 11+ vẫn không hoạt động**

**Nguyên nhân:**
- Cần quyền `MANAGE_EXTERNAL_STORAGE` đặc biệt
- Không thể cấp qua runtime permission thông thường

**Giải pháp:**
1. **Mở Settings manually:**
   - Settings > Apps > Special app access
   - All files access > CCam Terminal > Allow
2. **Hoặc dùng ADB:**
   ```bash
   adb shell appops set cmc.ccam.terminal MANAGE_EXTERNAL_STORAGE allow
   ```

## 📊 Kiểm tra trạng thái quyền

### **Code để debug:**
```java
// Log permission status
PermissionHelper.logPermissionStatus(context);

// Check specific permissions
boolean hasStorage = PermissionHelper.hasStoragePermission(context);
Log.d("Permission", "Has storage permission: " + hasStorage);
```

### **ADB commands:**
```bash
# Kiểm tra quyền app
adb shell dumpsys package cmc.ccam.terminal | grep -A5 -B5 permission

# Kiểm tra MANAGE_EXTERNAL_STORAGE
adb shell appops get cmc.ccam.terminal MANAGE_EXTERNAL_STORAGE

# Cấp quyền MANAGE_EXTERNAL_STORAGE
adb shell appops set cmc.ccam.terminal MANAGE_EXTERNAL_STORAGE allow
```

## 🎯 Kết quả mong đợi

Sau khi cấp quyền đúng cách:

```
D  PERMISSION STATUS
D  Android Version: 15 (API 35)
D  MANAGE_EXTERNAL_STORAGE: true
D  External Storage State: mounted
D  Found license file at: /storage/emulated/0/ArcFacePro32.dat
D  ✅ Copy successful! Destination file size: 2048 bytes
D  ✅ File integrity verified - sizes match
D  Final activation result: ✅ SUCCESS
```

## 💡 Lưu ý quan trọng

1. **Android 11+** cần quyền `MANAGE_EXTERNAL_STORAGE` đặc biệt
2. **Android 13+** cần quyền `READ_MEDIA_*` thay vì `READ_EXTERNAL_STORAGE`
3. **Restart app** sau khi cấp quyền để đảm bảo quyền được áp dụng
4. **File license** phải có kích thước > 100 bytes để được coi là hợp lệ
