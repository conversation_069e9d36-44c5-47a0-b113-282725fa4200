<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_show"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="@dimen/attr_image_max_height"
            android:adjustViewBounds="true" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/bt_choose_local_image"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:onClick="chooseLocalImage"
                android:text="@string/choose_local_image" />

            <Button
                android:id="@+id/bt_compare"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:onClick="process"
                android:text="@string/start_process" />
        </LinearLayout>

        <TextView
            android:layout_marginStart="@dimen/common_margin"
            android:layout_marginEnd="@dimen/common_margin"
            android:id="@+id/tv_notice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>
</ScrollView>