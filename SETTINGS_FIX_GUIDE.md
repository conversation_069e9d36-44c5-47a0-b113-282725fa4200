# 🔧 Hướng dẫn sửa lỗi Settings cho CCam Terminal

## 🚨 Vấn đề gốc

**Lỗi ClassNotFoundException:**
```
java.lang.ClassNotFoundException: com.arcsoft.arcfacedemo.preference.ChooseDetectDegreeListPreference
```

**Nguyên nhân:**
- File XML `preferences_recognize.xml` đang tham chiếu đến package cũ `com.arcsoft.arcfacedemo`
- App đã được migrate từ package demo sang `cmc.ccam.terminal`
- Các custom preference classes tồn tại nhưng bị tham chiếu sai package

## ✅ Giải pháp đã triển khai

### 1. **Sửa lại package references trong XML**

**File:** `app/src/main/res/xml/preferences_recognize.xml`

**Thay đổi:**
```xml
<!-- CŨ - SAI -->
<com.arcsoft.arcfacedemo.preference.ChooseDetectDegreeListPreference ... />
<com.arcsoft.arcfacedemo.preference.ThresholdPreference ... />

<!-- MỚI - ĐÚNG -->
<cmc.ccam.terminal.preference.ChooseDetectDegreeListPreference ... />
<cmc.ccam.terminal.preference.ThresholdPreference ... />
```

**Các class đã được sửa:**
- ✅ `ChooseDetectDegreeListPreference`
- ✅ `ThresholdPreference` (tất cả instances)
- ✅ Intent target cho `CameraConfigureActivity`

### 2. **Thêm ProGuard rules bảo vệ**

**File:** `app/proguard-rules.pro`

```proguard
# Keep custom preference classes
-keep class cmc.ccam.terminal.preference.** { *; }

# Keep preference constructors that are called via reflection
-keepclassmembers class * extends androidx.preference.Preference {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
}

# Keep widget classes used by preferences
-keep class cmc.ccam.terminal.widget.** { *; }
```

### 3. **Tạo fallback mechanism**

**File:** `RecognizeSettingsActivity.java`

- ✅ **Try-catch wrapper** cho việc load preferences
- ✅ **Fallback** đến version đơn giản nếu lỗi
- ✅ **Logging chi tiết** để debug
- ✅ **User notification** khi có lỗi

### 4. **Tạo test activity để debug**

**File:** `TestSettingsActivity.java`

- ✅ **Class loading test** để verify các preference classes
- ✅ **Step-by-step loading** để isolate issues
- ✅ **Detailed logging** cho debugging

### 5. **Tạo simplified preferences**

**File:** `preferences_recognize_simple.xml`

- ✅ **Standard preferences only** (không có custom classes)
- ✅ **Fallback option** khi custom preferences fail
- ✅ **Core functionality** vẫn hoạt động

## 🔍 Cách kiểm tra fix

### **Phương pháp 1: Chạy test script**
```bash
test_settings_fix.bat
```

**Kết quả mong đợi:**
```
✅ Successfully loaded preferences_recognize.xml
✅ ChooseDetectDegreeListPreference class found
✅ ThresholdPreference class found
```

### **Phương pháp 2: Manual testing**
1. **Build và install app**
2. **Mở Settings** từ main menu
3. **Kiểm tra log:**
   ```bash
   adb logcat | grep -E "(RecognizeSettings|TestSettings)"
   ```

### **Phương pháp 3: Test specific activity**
```bash
adb shell am start -n cmc.ccam.terminal/.ui.activity.TestSettingsActivity
```

## 🚨 Troubleshooting

### **Lỗi: "Still getting ClassNotFoundException"**

**Nguyên nhân có thể:**
- Cache chưa được clear
- APK cũ vẫn còn trên device
- ProGuard rules chưa được apply

**Giải pháp:**
```bash
# Clean và rebuild
gradlew.bat clean
gradlew.bat assembleDebug

# Uninstall và reinstall
adb uninstall cmc.ccam.terminal
adb install app\build\outputs\apk\debug\app-debug.apk
```

### **Lỗi: "Settings screen blank"**

**Nguyên nhân:**
- XML syntax error
- Missing string resources
- Theme compatibility issue

**Giải pháp:**
1. **Kiểm tra XML syntax:**
   ```bash
   gradlew.bat assembleDebug
   ```
2. **Check string resources:**
   - Verify all `@string/` references exist
   - Check `@array/` references for ListPreferences

### **Lỗi: "Custom preferences not working"**

**Nguyên nhân:**
- Constructor signature mismatch
- Missing dependencies
- Android API compatibility

**Giải pháp:**
1. **Verify constructors** trong custom preference classes
2. **Check dependencies** trong build.gradle
3. **Test với standard preferences** trước

## 📊 Verification checklist

- [ ] **Build successful** - No compilation errors
- [ ] **App installs** - No installation issues  
- [ ] **Settings opens** - No crash when opening settings
- [ ] **Custom preferences load** - ChooseDetectDegreeListPreference works
- [ ] **Threshold preferences work** - All ThresholdPreference instances work
- [ ] **Camera configure opens** - Intent to CameraConfigureActivity works
- [ ] **Values save/load** - Preference values persist correctly

## 🎯 Kết quả mong đợi

**Log thành công:**
```
D RecognizeSettings: Loading preferences_recognize.xml...
D RecognizeSettings: ✅ Successfully loaded preferences_recognize.xml
D TestSettings: ✅ ChooseDetectDegreeListPreference class found
D TestSettings: ✅ ThresholdPreference class found
```

**UI hoạt động:**
- ✅ Settings screen mở không crash
- ✅ Tất cả preference items hiển thị
- ✅ Custom preferences hoạt động bình thường
- ✅ Values được save/load đúng cách

## 💡 Lưu ý quan trọng

1. **Package migration** - Đảm bảo tất cả references đều đã được update
2. **ProGuard protection** - Custom classes cần được protect khỏi obfuscation
3. **Fallback mechanism** - Luôn có backup plan khi custom preferences fail
4. **Testing** - Test trên nhiều Android versions để đảm bảo compatibility
5. **Logging** - Detailed logs giúp debug issues nhanh chóng

## 🔄 Next steps

Nếu vẫn có issues:
1. **Check specific Android version compatibility**
2. **Test với different themes**
3. **Verify all string/array resources**
4. **Consider migrating to newer preference APIs**
