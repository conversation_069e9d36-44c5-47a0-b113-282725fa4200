# Android Resource và Build Errors - <PERSON><PERSON>hắc <PERSON>ục

## 🔧 **Tóm tắt các lỗi đã sửa:**

### 1. **Android Manifest Compatibility (Android 12+)**
✅ **Đã khắc phục**: Thêm `android:exported` cho tất cả activities
- `HomeActivity`: `android:exported="true"` (launcher activity)
- Tất cả activities khác: `android:exported="false"` (internal activities)

### 2. **Switch-Case Resource ID Errors**
✅ **Đã khắc phục**: Thay thế switch-case bằng if-else statements trong 3 files:

#### **File 1: ThresholdLivePreferenceDialogFragmentCompat.java**
```java
// TRƯỚC (Lỗi):
switch (v.getId()) {
    case R.id.iv_live_increase:
        increaseLive();
        break;
    case R.id.iv_live_decrease:
        decreaseLive();
        break;
}

// SAU (Đã sửa):
int viewId = v.getId();
if (viewId == R.id.iv_live_increase) {
    increaseLive();
} else if (viewId == R.id.iv_live_decrease) {
    decreaseLive();
}
```

#### **File 2: ThresholdPreferenceDialogFragmentCompat.java**
```java
// TRƯỚC (Lỗi):
switch (v.getId()) {
    case R.id.iv_increase:
        increase();
        break;
    case R.id.iv_decrease:
        decrease();
        break;
}

// SAU (Đã sửa):
int viewId = v.getId();
if (viewId == R.id.iv_increase) {
    increase();
} else if (viewId == R.id.iv_decrease) {
    decrease();
}
```

#### **File 3: IntegerPreferenceDialogFragmentCompat.java**
```java
// Tương tự như trên, đã thay switch-case bằng if-else
```

### 3. **Layout Resources Verification**
✅ **Đã xác minh**: Tất cả layout files và view IDs tồn tại:
- `pref_live_threshold_setting.xml` → `iv_live_increase`, `iv_live_decrease`
- `pref_threshold_setting.xml` → `iv_increase`, `iv_decrease`

## 🚀 **Cách test build:**

### **Tùy chọn 1: Sử dụng script tự động**
```bash
.\build_with_java.bat
```

### **Tùy chọn 2: Test compilation only**
```bash
.\test_build.bat
```

### **Tùy chọn 3: Manual build (cần Java)**
```bash
# Thiết lập JAVA_HOME
set JAVA_HOME=C:\Program Files\Java\jdk-17
set PATH=%JAVA_HOME%\bin;%PATH%

# Clean và build
.\gradlew.bat clean build
```

## 📋 **Nguyên nhân lỗi:**

### **"constant expression required" Error**
- **Nguyên nhân**: Từ Java 17+ và Android Gradle Plugin 8.x, `R.id` values không còn là compile-time constants
- **Giải pháp**: Thay thế switch-case bằng if-else statements

### **Android Manifest Merger Error**
- **Nguyên nhân**: Android 12+ (API 31+) yêu cầu `android:exported` cho activities có intent-filter
- **Giải pháp**: Thêm `android:exported="true/false"` cho tất cả activities

## ✅ **Kết quả:**
- ✅ Manifest merger errors đã được khắc phục
- ✅ Switch-case resource ID errors đã được khắc phục  
- ✅ Layout resources đã được xác minh
- ✅ Project tương thích với Java 17+ và Android 12+

## 🔄 **Bước tiếp theo:**
1. Cài đặt Java JDK 17 hoặc cao hơn
2. Chạy `.\build_with_java.bat` để build project
3. Nếu có lỗi khác, kiểm tra log và báo cáo để được hỗ trợ thêm
