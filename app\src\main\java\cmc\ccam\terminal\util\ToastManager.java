package cmc.ccam.terminal.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

import java.util.HashMap;
import java.util.Map;

/**
 * ToastManager utility class to prevent excessive toast notifications
 * and avoid hitting Android's 50-toast limit per package.
 */
public class ToastManager {
    private static final String TAG = "ToastManager";
    
    // Maximum number of toasts to show per minute to prevent hitting Android's limit
    private static final int MAX_TOASTS_PER_MINUTE = 30;
    
    // Time window for counting toasts (1 minute in milliseconds)
    private static final long TIME_WINDOW_MS = 60 * 1000;
    
    // Minimum interval between identical toast messages (in milliseconds)
    private static final long MIN_DUPLICATE_INTERVAL_MS = 3000; // 3 seconds
    
    private static ToastManager instance;
    private final Handler mainHandler;
    
    // Track toast count and timing
    private int toastCount = 0;
    private long windowStartTime = 0;
    
    // Track last shown messages to prevent duplicates
    private final Map<String, Long> lastShownMessages = new HashMap<>();
    
    // Current toast reference to cancel if needed
    private Toast currentToast;
    
    private ToastManager() {
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    public static synchronized ToastManager getInstance() {
        if (instance == null) {
            instance = new ToastManager();
        }
        return instance;
    }
    
    /**
     * Show a short toast message with throttling
     * @param context Application context
     * @param message Message to display
     */
    public void showToast(Context context, String message) {
        showToast(context, message, Toast.LENGTH_SHORT);
    }
    
    /**
     * Show a long toast message with throttling
     * @param context Application context
     * @param message Message to display
     */
    public void showLongToast(Context context, String message) {
        showToast(context, message, Toast.LENGTH_LONG);
    }
    
    /**
     * Show a toast message with throttling and duplicate prevention
     * @param context Application context
     * @param message Message to display
     * @param duration Toast duration (Toast.LENGTH_SHORT or Toast.LENGTH_LONG)
     */
    public void showToast(Context context, String message, int duration) {
        if (context == null || message == null || message.trim().isEmpty()) {
            return;
        }
        
        final long currentTime = System.currentTimeMillis();
        
        // Check if we're within the rate limit
        if (!isWithinRateLimit(currentTime)) {
            // Silently drop the toast if we're over the limit
            return;
        }
        
        // Check for duplicate messages
        if (isDuplicateMessage(message, currentTime)) {
            return;
        }
        
        // Show the toast on the main thread
        if (Looper.myLooper() == Looper.getMainLooper()) {
            showToastInternal(context, message, duration, currentTime);
        } else {
            mainHandler.post(() -> showToastInternal(context, message, duration, currentTime));
        }
    }
    
    /**
     * Force show a toast message, bypassing throttling (use sparingly)
     * @param context Application context
     * @param message Message to display
     * @param duration Toast duration
     */
    public void forceShowToast(Context context, String message, int duration) {
        if (context == null || message == null || message.trim().isEmpty()) {
            return;
        }
        
        if (Looper.myLooper() == Looper.getMainLooper()) {
            createAndShowToast(context, message, duration);
        } else {
            mainHandler.post(() -> createAndShowToast(context, message, duration));
        }
    }
    
    private void showToastInternal(Context context, String message, int duration, long currentTime) {
        // Update tracking
        updateToastCount(currentTime);
        lastShownMessages.put(message, currentTime);
        
        createAndShowToast(context, message, duration);
    }
    
    private void createAndShowToast(Context context, String message, int duration) {
        // Cancel current toast if exists
        if (currentToast != null) {
            currentToast.cancel();
        }
        
        // Create and show new toast
        currentToast = Toast.makeText(context.getApplicationContext(), message, duration);
        currentToast.show();
    }
    
    private boolean isWithinRateLimit(long currentTime) {
        // Reset window if needed
        if (currentTime - windowStartTime > TIME_WINDOW_MS) {
            windowStartTime = currentTime;
            toastCount = 0;
        }
        
        return toastCount < MAX_TOASTS_PER_MINUTE;
    }
    
    private boolean isDuplicateMessage(String message, long currentTime) {
        Long lastShown = lastShownMessages.get(message);
        if (lastShown != null) {
            return (currentTime - lastShown) < MIN_DUPLICATE_INTERVAL_MS;
        }
        return false;
    }
    
    private void updateToastCount(long currentTime) {
        // Reset window if needed
        if (currentTime - windowStartTime > TIME_WINDOW_MS) {
            windowStartTime = currentTime;
            toastCount = 0;
        }
        
        toastCount++;
    }
    
    /**
     * Clear the duplicate message cache (useful when starting new activities)
     */
    public void clearDuplicateCache() {
        lastShownMessages.clear();
    }
    
    /**
     * Get current toast count in the current time window
     * @return Current toast count
     */
    public int getCurrentToastCount() {
        return toastCount;
    }
    
    /**
     * Check if toast rate limiting is currently active
     * @return true if rate limiting is active
     */
    public boolean isRateLimited() {
        return !isWithinRateLimit(System.currentTimeMillis());
    }
}
