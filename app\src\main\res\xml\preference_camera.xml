<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <PreferenceCategory app:title="@string/title_camera_preview_adaption">
        <ListPreference
            app:key="@string/preference_dual_camera_preview_size"
            app:title="@string/title_dual_camera_preview_size"/>

        <ListPreference
            app:defaultValue="0"
            app:entries="@array/camera_preview_additional_rotation"
            app:entryValues="@array/camera_preview_additional_rotation"
            app:key="@string/preference_rgb_camera_rotation"
            app:title="@string/title_rgb_preview_additional_rotation"
            app:useSimpleSummaryProvider="true" />

        <ListPreference
            app:defaultValue="0"
            app:entries="@array/camera_preview_additional_rotation"
            app:entryValues="@array/camera_preview_additional_rotation"
            app:key="@string/preference_ir_camera_rotation"
            app:title="@string/title_ir_preview_additional_rotation"
            app:useSimpleSummaryProvider="true" />

        <SwitchPreferenceCompat
            app:defaultValue="false"
            app:key="@string/preference_switch_camera"
            app:summaryOff="@string/description_camera_0_rgb_1_ir"
            app:summaryOn="@string/description_camera_0_ir_1_rgb"
            app:title="@string/title_switch_camera"/>
    </PreferenceCategory>


</PreferenceScreen>