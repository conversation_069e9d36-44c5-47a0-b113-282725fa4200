<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application
        android:requestLegacyExternalStorage="true"
        android:name=".ArcFaceApplication"
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".ui.activity.ActivationActivity"
            android:label="@string/active_engine"
            android:exported="false"/>
        <activity
            android:name=".ui.activity.LivenessDetectActivity"
            android:launchMode="singleTop"
            android:theme="@style/FullScreenTheme"
            android:exported="false" />
        <activity
            android:name=".ui.activity.CameraConfigureActivity"
            android:launchMode="singleTop"
            android:theme="@style/FullScreenTheme"
            android:exported="false" />
        <activity
            android:name=".ui.activity.DataLengthCalculatorActivity"
            android:label="@string/page_calculate_data_length"
            android:launchMode="singleTop"
            android:exported="false" />
        <activity
            android:name=".ui.activity.RecognizeSettingsActivity"
            android:label="@string/page_settings"
            android:launchMode="singleTop"
            android:exported="false" />
        <activity
            android:name=".ui.activity.HomeActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activity.FaceManageActivity"
            android:launchMode="singleTop"
            android:theme="@style/AppTheme.NoActionBar"
            android:exported="false" />
        <activity
            android:name=".ui.activity.RegisterAndRecognizeActivity"
            android:launchMode="singleTop"
            android:theme="@style/FullScreenTheme"
            android:exported="false" />
        <activity
            android:name=".ui.activity.RecognizeDebugActivity"
            android:launchMode="singleTop"
            android:theme="@style/FullScreenTheme"
            android:exported="false" />
        <activity
            android:name=".ui.activity.ImageFaceAttrDetectActivity"
            android:launchMode="singleTop"
            android:theme="@style/FullScreenTheme"
            android:exported="false" />
        <activity
            android:name=".ui.activity.FaceCompareActivity"
            android:launchMode="singleTop"
            android:theme="@style/FullScreenTheme"
            android:exported="false" />
    </application>
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

</manifest>