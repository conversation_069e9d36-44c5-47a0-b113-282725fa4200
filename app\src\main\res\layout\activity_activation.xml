<?xml version="1.0" encoding="utf-8"?>
<layout>
    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.activity.ActivationActivity">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/common_margin"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:textColor="@color/colorAccent"
                android:layout_gravity="start"
                android:text="@string/active_offline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:layout_gravity="start"
                android:text="@string/notice_active_offline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <Button
                android:text="@string/active_offline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="activeOffline"/>
            <TextView
                android:layout_marginTop="@dimen/common_margin"
                android:textColor="@color/colorAccent"
                android:layout_gravity="start"
                android:text="@string/copy_device_finger"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <Button
                android:text="@string/copy_device_finger"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="copyDeviceFinger"/>
        </LinearLayout>
    </ScrollView>
    <data>
        <variable
            name="appId"
            type="String" />
        <variable
            name="sdkKey"
            type="String" />
        <variable
            name="activeKey"
            type="String" />
    </data>
</layout>