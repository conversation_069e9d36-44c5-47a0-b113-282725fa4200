@echo off
echo ========================================
echo Testing Android Build (Resource Fix)
echo ========================================

REM Try to set a common Java path temporarily
if exist "C:\Program Files\Java\jdk-17\bin\java.exe" (
    set JAVA_HOME=C:\Program Files\Java\jdk-17
    echo Found Java 17 at: %JAVA_HOME%
    goto :build
)

if exist "C:\Program Files\Java\jdk-11\bin\java.exe" (
    set JAVA_HOME=C:\Program Files\Java\jdk-11
    echo Found Java 11 at: %JAVA_HOME%
    goto :build
)

if exist "C:\Program Files\Java\jdk1.8.0_301\bin\java.exe" (
    set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_301
    echo Found Java 8 at: %JAVA_HOME%
    goto :build
)

echo Java not found in common locations.
echo Please install Java or run build_with_java.bat for more options.
pause
exit /b 1

:build
echo.
echo Using Java: %JAVA_HOME%
set PATH=%JAVA_HOME%\bin;%PATH%

echo.
echo Testing compilation only (no full build)...
echo.

REM Try to compile only to check for syntax errors
call gradlew.bat compileDebugJavaWithJavac

if errorlevel 1 (
    echo.
    echo Compilation failed! Check the error messages above.
    echo.
) else (
    echo.
    echo Compilation successful! Resource and syntax errors are fixed.
    echo.
)

pause
