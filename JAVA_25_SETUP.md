# Hướng dẫn cài đặt và cấu hình Java 25 cho Android Project

## 1. Cài đặt Java 25

### Tải Java 25 (OpenJDK)
- <PERSON><PERSON><PERSON> cập: https://jdk.java.net/25/
- T<PERSON>i phiên bản Windows x64 (zip file)
- <PERSON><PERSON><PERSON><PERSON> nén vào thư mục như: `C:\Program Files\Java\jdk-25`

### Hoặc sử dụng SDKMAN (khuyến nghị)
```bash
# Cài đặt SDKMAN
curl -s "https://get.sdkman.io" | bash

# Cài đặt Java 25
sdk install java 25-open
sdk use java 25-open
```

## 2. Thiết lập biến môi trường

### Windows
1. Mở System Properties → Advanced → Environment Variables
2. Thêm/cập nhật các biến:
   - `JAVA_HOME`: `C:\Program Files\Java\jdk-25`
   - `PATH`: Thêm `%JAVA_HOME%\bin`

### <PERSON><PERSON><PERSON> tra cài đặt
```bash
java -version
javac -version
```

## 3. <PERSON><PERSON><PERSON> hình project cho Java 25

### Cập nhật app/build.gradle
```gradle
android {
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_25
        targetCompatibility JavaVersion.VERSION_25
    }
}
```

### Cập nhật gradle.properties
```properties
# Java toolchain configuration for Java 25
org.gradle.java.home=C:\\Program Files\\Java\\jdk-25
org.gradle.java.installations.auto-detect=true
org.gradle.java.installations.auto-download=true
```

## 4. Build project
```bash
./gradlew clean build
```

## 5. Lưu ý quan trọng

- Java 25 là phiên bản preview, có thể không ổn định cho production
- Khuyến nghị sử dụng Java 17 hoặc 21 cho production
- Đảm bảo Android Studio hỗ trợ Java 25
- Kiểm tra compatibility với các dependencies

## 6. Troubleshooting

### Lỗi "JAVA_HOME not set"
- Kiểm tra biến môi trường JAVA_HOME
- Restart terminal/IDE sau khi set biến môi trường

### Lỗi "Unsupported Java version"
- Cập nhật Android Gradle Plugin lên phiên bản mới nhất
- Cập nhật Gradle Wrapper lên phiên bản 8.15+

### Build fails
- Thử clean project: `./gradlew clean`
- Xóa .gradle folder và rebuild
- Kiểm tra dependencies compatibility
